import { createFileRoute, redirect } from '@tanstack/react-router'
import { createServerFn } from '@tanstack/react-start'
import { Users, Ship as ShipIcon, Calendar, Settings, BarChart3 } from 'lucide-react'

import { getSession } from '../utils/session'
import { storage } from '../utils/storage'
import type { User } from '../utils/storage'

// Server functions
const getUser = createServerFn({ method: 'GET' }).handler(async () => {
  const session = await getSession()
  if (!session.userId) {
    return null
  }
  
  const user = await storage.getUser(session.userId)
  if (!user) {
    return null
  }
  
  const { password: _, ...userWithoutPassword } = user
  return userWithoutPassword
})

const getAdminStats = createServerFn({ method: 'GET' }).handler(async () => {
  const session = await getSession()
  if (!session.userId) {
    throw new Error('Not authenticated')
  }

  const user = await storage.getUser(session.userId)
  if (!user?.isAdmin) {
    throw new Error('Access denied')
  }

  // Get admin statistics
  const [allUsers, allShips, allDocks] = await Promise.all([
    storage.getAllUsers(),
    storage.getAllShipsForAdmin(),
    storage.getAllDocks(),
  ])

  return {
    totalUsers: allUsers.length,
    totalShips: allShips.length,
    totalDocks: allDocks.length,
    recentUsers: allUsers.slice(-5).reverse(),
    recentShips: allShips.slice(-5).reverse(),
  }
})

export const Route = createFileRoute('/admin')({
  beforeLoad: async () => {
    const user = await getUser()
    if (!user) {
      throw redirect({ to: '/login' })
    }
    if (!user.isAdmin) {
      throw redirect({ to: '/' })
    }
  },
  loader: async () => {
    const [user, adminStats] = await Promise.all([
      getUser(),
      getAdminStats(),
    ])
    return { user, ...adminStats }
  },
  component: AdminPage,
})

function AdminPage() {
  const { user, totalUsers, totalShips, totalDocks, recentUsers, recentShips } = Route.useLoaderData()

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="space-y-6">
            {/* Header */}
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-gray-500">Systemübersicht und Verwaltung</p>
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Users className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Benutzer
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {totalUsers}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <ShipIcon className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Schiffe
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {totalShips}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Calendar className="h-6 w-6 text-yellow-600" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Anlegestellen
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {totalDocks}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <BarChart3 className="h-6 w-6 text-purple-600" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Auslastung
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          85%
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white shadow overflow-hidden sm:rounded-lg">
              <div className="px-4 py-5 sm:px-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Schnellzugriff
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                  Häufig verwendete Verwaltungsfunktionen
                </p>
              </div>
              <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <a
                    href="/admin/users"
                    className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 rounded-lg border border-gray-300 hover:border-gray-400"
                  >
                    <div>
                      <span className="rounded-lg inline-flex p-3 bg-blue-50 text-blue-700 ring-4 ring-white">
                        <Users className="h-6 w-6" />
                      </span>
                    </div>
                    <div className="mt-4">
                      <h3 className="text-lg font-medium">
                        <span className="absolute inset-0" aria-hidden="true" />
                        Benutzer verwalten
                      </h3>
                      <p className="mt-2 text-sm text-gray-500">
                        Benutzerkonten anzeigen, bearbeiten und verwalten
                      </p>
                    </div>
                  </a>

                  <a
                    href="/admin/ships"
                    className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 rounded-lg border border-gray-300 hover:border-gray-400"
                  >
                    <div>
                      <span className="rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white">
                        <ShipIcon className="h-6 w-6" />
                      </span>
                    </div>
                    <div className="mt-4">
                      <h3 className="text-lg font-medium">
                        <span className="absolute inset-0" aria-hidden="true" />
                        Schiffe verwalten
                      </h3>
                      <p className="mt-2 text-sm text-gray-500">
                        Alle registrierten Schiffe anzeigen und verwalten
                      </p>
                    </div>
                  </a>

                  <a
                    href="/admin/settings"
                    className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 rounded-lg border border-gray-300 hover:border-gray-400"
                  >
                    <div>
                      <span className="rounded-lg inline-flex p-3 bg-purple-50 text-purple-700 ring-4 ring-white">
                        <Settings className="h-6 w-6" />
                      </span>
                    </div>
                    <div className="mt-4">
                      <h3 className="text-lg font-medium">
                        <span className="absolute inset-0" aria-hidden="true" />
                        Systemeinstellungen
                      </h3>
                      <p className="mt-2 text-sm text-gray-500">
                        Konfiguration und Systemeinstellungen verwalten
                      </p>
                    </div>
                  </a>
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Recent Users */}
              <div className="bg-white shadow overflow-hidden sm:rounded-lg">
                <div className="px-4 py-5 sm:px-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Neueste Benutzer
                  </h3>
                </div>
                <ul className="divide-y divide-gray-200">
                  {recentUsers.map((user: any) => (
                    <li key={user.id} className="px-4 py-4 sm:px-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="flex-shrink-0">
                            <Users className="h-6 w-6 text-gray-400" />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {user.companyName}
                            </div>
                            <div className="text-sm text-gray-500">
                              {user.email}
                            </div>
                          </div>
                        </div>
                        <div className="text-sm text-gray-500">
                          {user.createdAt ? new Date(user.createdAt).toLocaleDateString('de-DE') : ''}
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Recent Ships */}
              <div className="bg-white shadow overflow-hidden sm:rounded-lg">
                <div className="px-4 py-5 sm:px-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Neueste Schiffe
                  </h3>
                </div>
                <ul className="divide-y divide-gray-200">
                  {recentShips.map((ship: any) => (
                    <li key={ship.id} className="px-4 py-4 sm:px-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="flex-shrink-0">
                            <ShipIcon className="h-6 w-6 text-gray-400" />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {ship.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {ship.user.companyName}
                            </div>
                          </div>
                        </div>
                        <div className="text-sm text-gray-500">
                          {ship.createdAt ? new Date(ship.createdAt).toLocaleDateString('de-DE') : ''}
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
