export const translations = {
  de: {
    // Navigation
    dashboard: "Dashboard",
    ships: "Schi<PERSON>",
    bookings: "Buchungen",
    admin: "<PERSON><PERSON>",
    logout: "Abmelden",
    
    // Authentication
    login: "Anmelden",
    register: "Registrieren",
    email: "E-Mail",
    password: "Passwort",
    confirmPassword: "Passwort bestätigen",
    username: "Benutzername",
    companyName: "Firmenname",
    phoneNumber: "Telefonnummer",
    language: "Sprache",
    german: "Deutsch",
    english: "English",
    loginButton: "Anmelden",
    registerButton: "Registrieren",
    alreadyHaveAccount: "Bereits ein Konto?",
    noAccount: "Noch kein Konto?",
    
    // Ships
    myShips: "Meine Schiffe",
    addShip: "Schiff hinzufügen",
    shipName: "Schiffsname",
    length: "Länge (m)",
    width: "Breite (m)",
    maxPassengers: "Max. Passagiere",
    entryHeight1: "Einstiegshöhe 1 (cm)",
    entryHeight2: "Einstiegshöhe 2 (cm)",
    sundeckEntryHeight: "Einstiegshöhe Sonnendeck (cm)",
    entryDistanceFromBow: "Entfernung Einstieg vom Bug (m)",
    createShip: "Schiff erstellen",
    editShip: "Schiff bearbeiten",
    deleteShip: "Schiff löschen",
    noShips: "Keine Schiffe vorhanden",
    shipCreated: "Schiff erfolgreich erstellt",
    shipUpdated: "Schiff erfolgreich aktualisiert",
    shipDeleted: "Schiff erfolgreich gelöscht",
    
    // Bookings
    myBookings: "Meine Buchungen",
    createBooking: "Neue Buchung",
    dock: "Liegestelle",
    ship: "Schiff",
    arrivalDate: "Ankunftsdatum",
    arrivalTime: "Ankunftszeit",
    departureDate: "Abreisedatum", 
    departureTime: "Abreisezeit",
    notes: "Notizen",
    status: "Status",
    confirmed: "Bestätigt",
    pending: "Ausstehend",
    cancelled: "Storniert",
    bookingCreated: "Buchung erfolgreich erstellt",
    bookingUpdated: "Buchung erfolgreich aktualisiert",
    bookingDeleted: "Buchung erfolgreich gelöscht",
    noBookings: "Keine Buchungen vorhanden",
    editBooking: "Buchung bearbeiten",
    deleteBooking: "Buchung löschen",
    cannotEditBooking: "Diese Buchung kann nicht mehr bearbeitet werden",
    requestEditBooking: "Bearbeitungsanfrage stellen",
    
    // Additional Booking Fields
    embarking: "Einschiffung",
    disembarking: "Ausschiffung", 
    embarkingDisembarking: "Ein-/Ausschiffung",
    loadingDisposal: "Be-/Entladung",
    excursionWithBus: "Ausflug mit Bus",
    excursionWithoutBus: "Ausflug ohne Bus",
    routeFrom: "Route von",
    routeTo: "Route nach",
    optionalInformation: "Optionale Angaben",
    additionalInformation: "Zusätzliche Informationen",
    
    // Admin
    adminArea: "Admin-Bereich",
    settings: "Einstellungen",
    editingSettings: "Bearbeitungseinstellungen",
    editingCutoffTime: "Bearbeitungsschluss",
    editingStartTime: "Bearbeitungsstart",
    editingEndTime: "Bearbeitungsende",
    adminEmail: "Admin E-Mail",
    saveSettings: "Einstellungen speichern",
    settingsUpdated: "Einstellungen erfolgreich aktualisiert",
    
    // Booking Requests
    bookingRequests: "Bearbeitungsanfragen",
    pendingRequests: "Offene Anfragen",
    processedRequests: "Bearbeitete Anfragen",
    approve: "Genehmigen",
    reject: "Ablehnen",
    adminNotes: "Admin-Notizen",
    requestedBy: "Angefragt von",
    requestedChanges: "Gewünschte Änderungen",
    reason: "Grund",
    approved: "Genehmigt",
    rejected: "Abgelehnt",
    
    // Ship Management
    shipManagement: "Schiffsverwaltung",
    allShips: "Alle Schiffe",
    newShip: "Neues Schiff",
    owner: "Eigentümer",
    searchShips: "Schiffe suchen...",
    
    // User Management
    userManagement: "Benutzerverwaltung",
    allUsers: "Alle Benutzer",
    editUser: "Benutzer bearbeiten",
    deleteUser: "Benutzer löschen",
    adminRights: "Administrator-Rechte",
    userUpdated: "Benutzer erfolgreich aktualisiert",
    userDeleted: "Benutzer erfolgreich gelöscht",
    cannotDeleteSelf: "Sie können sich nicht selbst löschen",
    
    // Common
    save: "Speichern",
    cancel: "Abbrechen",
    edit: "Bearbeiten",
    delete: "Löschen",
    create: "Erstellen",
    update: "Aktualisieren",
    loading: "Laden...",
    error: "Fehler",
    success: "Erfolg",
    required: "Erforderlich",
    optional: "Optional",
    search: "Suchen",
    actions: "Aktionen",
    details: "Details",
    close: "Schließen",
    confirm: "Bestätigen",
    yes: "Ja",
    no: "Nein",
    
    // Error Messages
    invalidCredentials: "Ungültige Anmeldedaten",
    userNotFound: "Benutzer nicht gefunden",
    emailAlreadyExists: "E-Mail-Adresse bereits vorhanden",
    passwordMismatch: "Passwörter stimmen nicht überein",
    formValidationError: "Bitte überprüfen Sie Ihre Eingaben",
    serverError: "Serverfehler. Bitte versuchen Sie es später erneut",
    unauthorizedAccess: "Keine Berechtigung für diese Aktion",
    
    // Validation Messages
    emailRequired: "E-Mail ist erforderlich",
    passwordRequired: "Passwort ist erforderlich",
    usernameRequired: "Benutzername ist erforderlich",
    shipNameRequired: "Schiffsname ist erforderlich",
    dockRequired: "Anlegestelle ist erforderlich",
    shipRequired: "Schiff ist erforderlich",
    arrivalDateRequired: "Ankunftsdatum ist erforderlich",
    departureDateRequired: "Abreisedatum ist erforderlich",
  },
  
  en: {
    // Navigation
    dashboard: "Dashboard",
    ships: "Ships",
    bookings: "Bookings",
    admin: "Admin",
    logout: "Logout",
    
    // Authentication
    login: "Login",
    register: "Register",
    email: "Email",
    password: "Password",
    confirmPassword: "Confirm Password",
    username: "Username",
    companyName: "Company Name",
    phoneNumber: "Phone Number",
    language: "Language",
    german: "Deutsch",
    english: "English",
    loginButton: "Login",
    registerButton: "Register",
    alreadyHaveAccount: "Already have an account?",
    noAccount: "Don't have an account?",
    
    // Ships
    myShips: "My Ships",
    addShip: "Add Ship",
    shipName: "Ship Name",
    length: "Length (m)",
    width: "Width (m)",
    maxPassengers: "Max. Passengers",
    entryHeight1: "Entry Height 1 (cm)",
    entryHeight2: "Entry Height 2 (cm)",
    sundeckEntryHeight: "Sundeck Entry Height (cm)",
    entryDistanceFromBow: "Entry Distance from Bow (m)",
    createShip: "Create Ship",
    editShip: "Edit Ship",
    deleteShip: "Delete Ship",
    noShips: "No ships available",
    shipCreated: "Ship successfully created",
    shipUpdated: "Ship successfully updated",
    shipDeleted: "Ship successfully deleted",
    
    // Bookings
    myBookings: "My Bookings",
    createBooking: "New Booking",
    dock: "Dock",
    ship: "Ship",
    arrivalDate: "Arrival Date",
    arrivalTime: "Arrival Time",
    departureDate: "Departure Date",
    departureTime: "Departure Time",
    notes: "Notes",
    status: "Status",
    confirmed: "Confirmed",
    pending: "Pending",
    cancelled: "Cancelled",
    bookingCreated: "Booking successfully created",
    bookingUpdated: "Booking successfully updated",
    bookingDeleted: "Booking successfully deleted",
    noBookings: "No bookings available",
    editBooking: "Edit Booking",
    deleteBooking: "Delete Booking",
    cannotEditBooking: "This booking can no longer be edited",
    requestEditBooking: "Request Edit",
    
    // Additional Booking Fields
    embarking: "Embarking",
    disembarking: "Disembarking",
    embarkingDisembarking: "Embarking/Disembarking",
    loadingDisposal: "Loading/Disposal",
    excursionWithBus: "Excursion with Bus",
    excursionWithoutBus: "Excursion without Bus",
    routeFrom: "Route from",
    routeTo: "Route to",
    optionalInformation: "Optional Information",
    additionalInformation: "Additional Information",
    
    // Admin
    adminArea: "Admin Area",
    settings: "Settings",
    editingSettings: "Editing Settings",
    editingCutoffTime: "Editing Cutoff Time",
    editingStartTime: "Editing Start Time",
    editingEndTime: "Editing End Time",
    adminEmail: "Admin Email",
    saveSettings: "Save Settings",
    settingsUpdated: "Settings successfully updated",
    
    // Booking Requests
    bookingRequests: "Booking Requests",
    pendingRequests: "Pending Requests",
    processedRequests: "Processed Requests",
    approve: "Approve",
    reject: "Reject",
    adminNotes: "Admin Notes",
    requestedBy: "Requested by",
    requestedChanges: "Requested Changes",
    reason: "Reason",
    approved: "Approved",
    rejected: "Rejected",
    
    // Ship Management
    shipManagement: "Ship Management",
    allShips: "All Ships",
    newShip: "New Ship",
    owner: "Owner",
    searchShips: "Search ships...",
    
    // User Management
    userManagement: "User Management",
    allUsers: "All Users",
    editUser: "Edit User",
    deleteUser: "Delete User",
    adminRights: "Administrator Rights",
    userUpdated: "User successfully updated",
    userDeleted: "User successfully deleted",
    cannotDeleteSelf: "You cannot delete yourself",
    
    // Common
    save: "Save",
    cancel: "Cancel",
    edit: "Edit",
    delete: "Delete",
    create: "Create",
    update: "Update",
    loading: "Loading...",
    error: "Error",
    success: "Success",
    required: "Required",
    optional: "Optional",
    search: "Search",
    actions: "Actions",
    details: "Details",
    close: "Close",
    confirm: "Confirm",
    yes: "Yes",
    no: "No",
    
    // Error Messages
    invalidCredentials: "Invalid credentials",
    userNotFound: "User not found",
    emailAlreadyExists: "Email address already exists",
    passwordMismatch: "Passwords do not match",
    formValidationError: "Please check your inputs",
    serverError: "Server error. Please try again later",
    unauthorizedAccess: "No permission for this action",
    
    // Validation Messages
    emailRequired: "Email is required",
    passwordRequired: "Password is required",
    usernameRequired: "Username is required",
    shipNameRequired: "Ship name is required",
    dockRequired: "Dock is required",
    shipRequired: "Ship is required",
    arrivalDateRequired: "Arrival date is required",
    departureDateRequired: "Departure date is required",
  }
};

export type Language = 'de' | 'en';
export type TranslationKey = keyof typeof translations.de;

export function t(key: TranslationKey, language: Language = 'de'): string {
  return translations[language][key] || translations['de'][key] || key;
}