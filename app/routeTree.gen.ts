/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as LoginImport } from './routes/login'
import { Route as IndexImport } from './routes/index'
import { Route as ApiShipsIndexImport } from './routes/api/ships/index'
import { Route as ApiDocksIndexImport } from './routes/api/docks/index'
import { Route as ApiBookingsIndexImport } from './routes/api/bookings/index'
import { Route as ApiShipsIdImport } from './routes/api/ships/$id'
import { Route as ApiBookingsIdImport } from './routes/api/bookings/$id'
import { Route as ApiAuthUserImport } from './routes/api/auth/user'
import { Route as ApiAuthRegisterImport } from './routes/api/auth/register'
import { Route as ApiAuthLogoutImport } from './routes/api/auth/logout'
import { Route as ApiAuthLoginImport } from './routes/api/auth/login'

// Create/Update Routes

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const ApiShipsIndexRoute = ApiShipsIndexImport.update({
  id: '/api/ships/',
  path: '/api/ships/',
  getParentRoute: () => rootRoute,
} as any)

const ApiDocksIndexRoute = ApiDocksIndexImport.update({
  id: '/api/docks/',
  path: '/api/docks/',
  getParentRoute: () => rootRoute,
} as any)

const ApiBookingsIndexRoute = ApiBookingsIndexImport.update({
  id: '/api/bookings/',
  path: '/api/bookings/',
  getParentRoute: () => rootRoute,
} as any)

const ApiShipsIdRoute = ApiShipsIdImport.update({
  id: '/api/ships/$id',
  path: '/api/ships/$id',
  getParentRoute: () => rootRoute,
} as any)

const ApiBookingsIdRoute = ApiBookingsIdImport.update({
  id: '/api/bookings/$id',
  path: '/api/bookings/$id',
  getParentRoute: () => rootRoute,
} as any)

const ApiAuthUserRoute = ApiAuthUserImport.update({
  id: '/api/auth/user',
  path: '/api/auth/user',
  getParentRoute: () => rootRoute,
} as any)

const ApiAuthRegisterRoute = ApiAuthRegisterImport.update({
  id: '/api/auth/register',
  path: '/api/auth/register',
  getParentRoute: () => rootRoute,
} as any)

const ApiAuthLogoutRoute = ApiAuthLogoutImport.update({
  id: '/api/auth/logout',
  path: '/api/auth/logout',
  getParentRoute: () => rootRoute,
} as any)

const ApiAuthLoginRoute = ApiAuthLoginImport.update({
  id: '/api/auth/login',
  path: '/api/auth/login',
  getParentRoute: () => rootRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/api/auth/login': {
      id: '/api/auth/login'
      path: '/api/auth/login'
      fullPath: '/api/auth/login'
      preLoaderRoute: typeof ApiAuthLoginImport
      parentRoute: typeof rootRoute
    }
    '/api/auth/logout': {
      id: '/api/auth/logout'
      path: '/api/auth/logout'
      fullPath: '/api/auth/logout'
      preLoaderRoute: typeof ApiAuthLogoutImport
      parentRoute: typeof rootRoute
    }
    '/api/auth/register': {
      id: '/api/auth/register'
      path: '/api/auth/register'
      fullPath: '/api/auth/register'
      preLoaderRoute: typeof ApiAuthRegisterImport
      parentRoute: typeof rootRoute
    }
    '/api/auth/user': {
      id: '/api/auth/user'
      path: '/api/auth/user'
      fullPath: '/api/auth/user'
      preLoaderRoute: typeof ApiAuthUserImport
      parentRoute: typeof rootRoute
    }
    '/api/bookings/$id': {
      id: '/api/bookings/$id'
      path: '/api/bookings/$id'
      fullPath: '/api/bookings/$id'
      preLoaderRoute: typeof ApiBookingsIdImport
      parentRoute: typeof rootRoute
    }
    '/api/ships/$id': {
      id: '/api/ships/$id'
      path: '/api/ships/$id'
      fullPath: '/api/ships/$id'
      preLoaderRoute: typeof ApiShipsIdImport
      parentRoute: typeof rootRoute
    }
    '/api/bookings/': {
      id: '/api/bookings/'
      path: '/api/bookings'
      fullPath: '/api/bookings'
      preLoaderRoute: typeof ApiBookingsIndexImport
      parentRoute: typeof rootRoute
    }
    '/api/docks/': {
      id: '/api/docks/'
      path: '/api/docks'
      fullPath: '/api/docks'
      preLoaderRoute: typeof ApiDocksIndexImport
      parentRoute: typeof rootRoute
    }
    '/api/ships/': {
      id: '/api/ships/'
      path: '/api/ships'
      fullPath: '/api/ships'
      preLoaderRoute: typeof ApiShipsIndexImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/login': typeof LoginRoute
  '/api/auth/login': typeof ApiAuthLoginRoute
  '/api/auth/logout': typeof ApiAuthLogoutRoute
  '/api/auth/register': typeof ApiAuthRegisterRoute
  '/api/auth/user': typeof ApiAuthUserRoute
  '/api/bookings/$id': typeof ApiBookingsIdRoute
  '/api/ships/$id': typeof ApiShipsIdRoute
  '/api/bookings': typeof ApiBookingsIndexRoute
  '/api/docks': typeof ApiDocksIndexRoute
  '/api/ships': typeof ApiShipsIndexRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/login': typeof LoginRoute
  '/api/auth/login': typeof ApiAuthLoginRoute
  '/api/auth/logout': typeof ApiAuthLogoutRoute
  '/api/auth/register': typeof ApiAuthRegisterRoute
  '/api/auth/user': typeof ApiAuthUserRoute
  '/api/bookings/$id': typeof ApiBookingsIdRoute
  '/api/ships/$id': typeof ApiShipsIdRoute
  '/api/bookings': typeof ApiBookingsIndexRoute
  '/api/docks': typeof ApiDocksIndexRoute
  '/api/ships': typeof ApiShipsIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/login': typeof LoginRoute
  '/api/auth/login': typeof ApiAuthLoginRoute
  '/api/auth/logout': typeof ApiAuthLogoutRoute
  '/api/auth/register': typeof ApiAuthRegisterRoute
  '/api/auth/user': typeof ApiAuthUserRoute
  '/api/bookings/$id': typeof ApiBookingsIdRoute
  '/api/ships/$id': typeof ApiShipsIdRoute
  '/api/bookings/': typeof ApiBookingsIndexRoute
  '/api/docks/': typeof ApiDocksIndexRoute
  '/api/ships/': typeof ApiShipsIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/login'
    | '/api/auth/login'
    | '/api/auth/logout'
    | '/api/auth/register'
    | '/api/auth/user'
    | '/api/bookings/$id'
    | '/api/ships/$id'
    | '/api/bookings'
    | '/api/docks'
    | '/api/ships'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/login'
    | '/api/auth/login'
    | '/api/auth/logout'
    | '/api/auth/register'
    | '/api/auth/user'
    | '/api/bookings/$id'
    | '/api/ships/$id'
    | '/api/bookings'
    | '/api/docks'
    | '/api/ships'
  id:
    | '__root__'
    | '/'
    | '/login'
    | '/api/auth/login'
    | '/api/auth/logout'
    | '/api/auth/register'
    | '/api/auth/user'
    | '/api/bookings/$id'
    | '/api/ships/$id'
    | '/api/bookings/'
    | '/api/docks/'
    | '/api/ships/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  LoginRoute: typeof LoginRoute
  ApiAuthLoginRoute: typeof ApiAuthLoginRoute
  ApiAuthLogoutRoute: typeof ApiAuthLogoutRoute
  ApiAuthRegisterRoute: typeof ApiAuthRegisterRoute
  ApiAuthUserRoute: typeof ApiAuthUserRoute
  ApiBookingsIdRoute: typeof ApiBookingsIdRoute
  ApiShipsIdRoute: typeof ApiShipsIdRoute
  ApiBookingsIndexRoute: typeof ApiBookingsIndexRoute
  ApiDocksIndexRoute: typeof ApiDocksIndexRoute
  ApiShipsIndexRoute: typeof ApiShipsIndexRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  LoginRoute: LoginRoute,
  ApiAuthLoginRoute: ApiAuthLoginRoute,
  ApiAuthLogoutRoute: ApiAuthLogoutRoute,
  ApiAuthRegisterRoute: ApiAuthRegisterRoute,
  ApiAuthUserRoute: ApiAuthUserRoute,
  ApiBookingsIdRoute: ApiBookingsIdRoute,
  ApiShipsIdRoute: ApiShipsIdRoute,
  ApiBookingsIndexRoute: ApiBookingsIndexRoute,
  ApiDocksIndexRoute: ApiDocksIndexRoute,
  ApiShipsIndexRoute: ApiShipsIndexRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/login",
        "/api/auth/login",
        "/api/auth/logout",
        "/api/auth/register",
        "/api/auth/user",
        "/api/bookings/$id",
        "/api/ships/$id",
        "/api/bookings/",
        "/api/docks/",
        "/api/ships/"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/api/auth/login": {
      "filePath": "api/auth/login.ts"
    },
    "/api/auth/logout": {
      "filePath": "api/auth/logout.ts"
    },
    "/api/auth/register": {
      "filePath": "api/auth/register.ts"
    },
    "/api/auth/user": {
      "filePath": "api/auth/user.ts"
    },
    "/api/bookings/$id": {
      "filePath": "api/bookings/$id.ts"
    },
    "/api/ships/$id": {
      "filePath": "api/ships/$id.ts"
    },
    "/api/bookings/": {
      "filePath": "api/bookings/index.ts"
    },
    "/api/docks/": {
      "filePath": "api/docks/index.ts"
    },
    "/api/ships/": {
      "filePath": "api/ships/index.ts"
    }
  }
}
ROUTE_MANIFEST_END */
