/**
 * Formats a date as a string in the format DD.MM.YYYY
 */
export function formatDate(date: Date | string): string {
  const d = new Date(date);
  return d.toLocaleDateString('de-DE', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
}

/**
 * Formats a time as a string in the format HH:MM
 */
export function formatTime(date: Date | string): string {
  const d = new Date(date);
  return d.toLocaleTimeString('de-DE', {
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * Formats a date and time as a string in the format DD.MM.YYYY HH:MM
 */
export function formatDateTime(date: Date | string): string {
  const d = new Date(date);
  return `${formatDate(d)} ${formatTime(d)}`;
}

/**
 * Converts a date and time strings to a Date object
 */
export function dateTimeToISOString(dateString: string, timeString: string): string {
  const [day, month, year] = dateString.split('.');
  const [hour, minute] = timeString.split(':');
  
  // Create date in local timezone
  const date = new Date(
    parseInt(year),
    parseInt(month) - 1,
    parseInt(day),
    parseInt(hour),
    parseInt(minute)
  );
  
  return date.toISOString();
}

/**
 * Convert ISO date string or Date object to date string in the format DD.MM.YYYY
 */
export function isoToDateString(isoDate: string | Date): string {
  if (isoDate === null || isoDate === undefined) {
    return '';
  }
  return formatDate(isoDate instanceof Date ? isoDate : new Date(isoDate));
}

/**
 * Convert ISO date string or Date object to time string in the format HH:MM
 */
export function isoToTimeString(isoDate: string | Date): string {
  if (isoDate === null || isoDate === undefined) {
    return '';
  }
  return formatTime(isoDate instanceof Date ? isoDate : new Date(isoDate));
}
