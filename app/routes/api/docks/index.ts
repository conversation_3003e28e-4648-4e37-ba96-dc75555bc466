import { createAPIFileRoute } from '@tanstack/react-start/api'
import { json } from '@tanstack/react-start'
import { storage } from '../../../utils/storage'
import { getSession } from '../../../utils/session'

export const Route = createAPIFileRoute('/api/docks')({
  GET: async () => {
    try {
      const session = await getSession()
      
      if (!session.userId) {
        return json({ message: 'Nicht authentifiziert' }, { status: 401 })
      }

      const docks = await storage.getAllDocks()
      return json(docks)
    } catch (error) {
      console.error('Get docks error:', error)
      return json({ message: 'Interner Serverfehler' }, { status: 500 })
    }
  },
})
