import { pgTable, text, serial, integer, boolean, timestamp, uniqueIndex } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// User schema for shipping companies
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  companyName: text("company_name").notNull(),
  email: text("email").notNull(),
  phoneNumber: text("phone_number"),
  language: text("language").default("de").notNull(), // de (Deutsch) oder en (English)
  isAdmin: boolean("is_admin").default(false).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const insertUserSchema = createInsertSchema(users).omit({
  id: true,
  createdAt: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

// Ship schema
export const ships = pgTable("ships", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: 'cascade' }),
  length: integer("length"), // in meters
  width: integer("width"), // in meters
  entryHeight1: integer("entry_height_1"), // Einstiegshöhe 1 in cm
  entryHeight2: integer("entry_height_2"), // Einstiegshöhe 2 in cm
  sundeckEntryHeight: integer("sundeck_entry_height"), // Einstiegshöhe Sonnendeck in cm
  entryDistanceFromBow: integer("entry_distance_from_bow"), // Entfernung des Einstiegs vom Bug in meters
  maxPassengers: integer("max_passengers"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const insertShipSchema = createInsertSchema(ships).omit({
  id: true,
  createdAt: true,
});

export type InsertShip = z.infer<typeof insertShipSchema>;
export type Ship = typeof ships.$inferSelect;

// Dock schema for landing locations
export const docks = pgTable("docks", {
  id: serial("id").primaryKey(),
  name: text("name").notNull().unique(),
  location: text("location").notNull(),
  maxShipLength: integer("max_ship_length"), // in meters
  isActive: boolean("is_active").default(true).notNull(),
});

export const insertDockSchema = createInsertSchema(docks).omit({
  id: true,
});

export type InsertDock = z.infer<typeof insertDockSchema>;
export type Dock = typeof docks.$inferSelect;

// Booking schema for ship docking reservations
export const bookings = pgTable("bookings", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: 'cascade' }),
  shipId: integer("ship_id").notNull().references(() => ships.id, { onDelete: 'cascade' }),
  dockId: integer("dock_id").notNull().references(() => docks.id, { onDelete: 'cascade' }),
  arrivalDate: timestamp("arrival_date").notNull(), // Ankunft (Pflichtfeld)
  departureDate: timestamp("departure_date").notNull(), // Abfahrt (Pflichtfeld)
  status: text("status").default("pending").notNull(), // pending, confirmed, cancelled
  
  // Optionale Felder
  embarking: text("embarking"), // Einschiffung
  disembarking: text("disembarking"), // Ausschiffung
  embarkingDisembarking: text("embarking_disembarking"), // Ein- und Ausschiffung
  loadingDisposal: text("loading_disposal"), // Loading / Entsorgung
  excursionWithBus: boolean("excursion_with_bus"), // Landausflug mit Bus
  excursionWithoutBus: boolean("excursion_without_bus"), // Landausflug ohne Bus
  routeFrom: text("route_from"), // Reiseroute von
  routeTo: text("route_to"), // Reiseroute bis
  
  row: text("row"), // optional row information
  notes: text("notes"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// WICHTIG: Komplett neues Schema mit manueller Definition
export const insertBookingSchema = z.object({
  shipId: z.number(),
  dockId: z.number(),
  userId: z.number(),
  status: z.string().default("pending"),
  
  // Pflichtfelder
  arrivalDate: z.union([
    z.string(),
    z.date()
  ]).transform(val => typeof val === 'string' ? new Date(val) : val),
  departureDate: z.union([
    z.string(),
    z.date()
  ]).transform(val => typeof val === 'string' ? new Date(val) : val),
  
  // Optionale Felder
  embarking: z.string().nullable().optional(),
  disembarking: z.string().nullable().optional(),
  embarkingDisembarking: z.string().nullable().optional(),
  loadingDisposal: z.string().nullable().optional(),
  excursionWithBus: z.boolean().nullable().optional(),
  excursionWithoutBus: z.boolean().nullable().optional(),
  routeFrom: z.string().nullable().optional(),
  routeTo: z.string().nullable().optional(),
  
  row: z.string().nullable().optional(),
  notes: z.string().nullable().optional(),
});

export type InsertBooking = z.infer<typeof insertBookingSchema>;
export type Booking = typeof bookings.$inferSelect;

// Extended booking type that includes related entities
export type BookingWithDetails = Booking & {
  ship: Ship;
  dock: Dock;
  user: Omit<User, 'password'>;
};

// Admin settings schema
export const adminSettings = pgTable("admin_settings", {
  id: serial("id").primaryKey(),
  editingCutoffTime: timestamp("editing_cutoff_time"), // Zeit ab der normale Bearbeitung nicht mehr möglich ist
  editingStartTime: timestamp("editing_start_time"), // Start des Sperrzeitraums
  editingEndTime: timestamp("editing_end_time"), // Ende des Sperrzeitraums
  adminEmail: text("admin_email"), // E-Mail-Adresse für Benachrichtigungen
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const insertAdminSettingsSchema = createInsertSchema(adminSettings).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export type InsertAdminSettings = z.infer<typeof insertAdminSettingsSchema>;
export type AdminSettings = typeof adminSettings.$inferSelect;

// Booking edit requests schema
export const bookingEditRequests = pgTable("booking_edit_requests", {
  id: serial("id").primaryKey(),
  bookingId: integer("booking_id").notNull().references(() => bookings.id, { onDelete: 'cascade' }),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: 'cascade' }),
  requestedChanges: text("requested_changes").notNull(), // JSON string mit den gewünschten Änderungen
  reason: text("reason"), // Begründung für die Änderungsanfrage
  status: text("status").default("pending").notNull(), // pending, approved, rejected
  adminNotes: text("admin_notes"),
  reviewedBy: integer("reviewed_by").references(() => users.id),
  reviewedAt: timestamp("reviewed_at"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const insertBookingEditRequestSchema = createInsertSchema(bookingEditRequests).omit({
  id: true,
  createdAt: true,
  reviewedAt: true,
});

export type InsertBookingEditRequest = z.infer<typeof insertBookingEditRequestSchema>;
export type BookingEditRequest = typeof bookingEditRequests.$inferSelect;

// Extended booking edit request type
export type BookingEditRequestWithDetails = BookingEditRequest & {
  booking: BookingWithDetails;
  user: Omit<User, 'password'>;
  reviewer?: Omit<User, 'password'>;
};
