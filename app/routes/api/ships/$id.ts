import { createAPIFileRoute } from '@tanstack/react-start/api'
import { json } from '@tanstack/react-start'
import { storage } from '../../../utils/storage'
import { getSession } from '../../../utils/session'

export const Route = createAPIFileRoute('/api/ships/$id')({
  GET: async ({ params }) => {
    try {
      const session = await getSession()
      
      if (!session.userId) {
        return json({ message: 'Nicht authentifiziert' }, { status: 401 })
      }

      const shipId = parseInt(params.id)
      if (isNaN(shipId)) {
        return json({ message: 'Ungültige Schiffs-ID' }, { status: 400 })
      }

      const ship = await storage.getShip(shipId)
      if (!ship) {
        return json({ message: 'Schiff nicht gefunden' }, { status: 404 })
      }

      // Check if user owns this ship
      if (ship.userId !== session.userId) {
        return json({ message: 'Zugriff verweigert' }, { status: 403 })
      }

      return json(ship)
    } catch (error) {
      console.error('Get ship error:', error)
      return json({ message: 'Interner Serverfehler' }, { status: 500 })
    }
  },

  PUT: async ({ params, request }) => {
    try {
      const session = await getSession()
      
      if (!session.userId) {
        return json({ message: 'Nicht authentifiziert' }, { status: 401 })
      }

      const shipId = parseInt(params.id)
      if (isNaN(shipId)) {
        return json({ message: 'Ungültige Schiffs-ID' }, { status: 400 })
      }

      const ship = await storage.getShip(shipId)
      if (!ship) {
        return json({ message: 'Schiff nicht gefunden' }, { status: 404 })
      }

      // Check if user owns this ship
      if (ship.userId !== session.userId) {
        return json({ message: 'Zugriff verweigert' }, { status: 403 })
      }

      const shipData = await request.json()
      const updatedShip = await storage.updateShip(shipId, shipData)

      return json(updatedShip)
    } catch (error) {
      console.error('Update ship error:', error)
      return json({ message: 'Interner Serverfehler' }, { status: 500 })
    }
  },

  DELETE: async ({ params }) => {
    try {
      const session = await getSession()
      
      if (!session.userId) {
        return json({ message: 'Nicht authentifiziert' }, { status: 401 })
      }

      const shipId = parseInt(params.id)
      if (isNaN(shipId)) {
        return json({ message: 'Ungültige Schiffs-ID' }, { status: 400 })
      }

      const ship = await storage.getShip(shipId)
      if (!ship) {
        return json({ message: 'Schiff nicht gefunden' }, { status: 404 })
      }

      // Check if user owns this ship
      if (ship.userId !== session.userId) {
        return json({ message: 'Zugriff verweigert' }, { status: 403 })
      }

      const deleted = await storage.deleteShip(shipId)
      if (!deleted) {
        return json({ message: 'Fehler beim Löschen des Schiffs' }, { status: 500 })
      }

      return json({ success: true })
    } catch (error) {
      console.error('Delete ship error:', error)
      return json({ message: 'Interner Serverfehler' }, { status: 500 })
    }
  },
})
