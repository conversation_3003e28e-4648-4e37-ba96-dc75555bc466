import { createAPIFileRoute } from '@tanstack/react-start/api'
import { json } from '@tanstack/react-start'
import { storage } from '../../../utils/storage'
import { getSession } from '../../../utils/session'

export const Route = createAPIFileRoute('/api/ships')({
  GET: async () => {
    try {
      const session = await getSession()
      
      if (!session.userId) {
        return json({ message: 'Nicht authentifiziert' }, { status: 401 })
      }

      const ships = await storage.getShipsByUser(session.userId)
      return json(ships)
    } catch (error) {
      console.error('Get ships error:', error)
      return json({ message: 'Interner Serverfehler' }, { status: 500 })
    }
  },

  POST: async ({ request }) => {
    try {
      const session = await getSession()
      
      if (!session.userId) {
        return json({ message: 'Nicht authentifiziert' }, { status: 401 })
      }

      const shipData = await request.json()
      
      if (!shipData.name) {
        return json({ message: 'Schiffsname ist erforderlich' }, { status: 400 })
      }

      const ship = await storage.createShip({
        ...shipData,
        userId: session.userId,
      })

      return json(ship, { status: 201 })
    } catch (error) {
      console.error('Create ship error:', error)
      return json({ message: 'Interner Serverfehler' }, { status: 500 })
    }
  },
})
