import { createAPIFileRoute } from '@tanstack/react-start/api'
import { json } from '@tanstack/react-start'
import { storage } from '../../../utils/storage'
import { getSession } from '../../../utils/session'

export const Route = createAPIFileRoute('/api/auth/user')({
  GET: async () => {
    try {
      const session = await getSession()
      
      if (!session.userId) {
        return json({ message: 'Nicht authentifiziert' }, { status: 401 })
      }

      const user = await storage.getUser(session.userId)
      if (!user) {
        return json({ message: 'Benutzer nicht gefunden' }, { status: 404 })
      }

      // Return user without password
      const { password: _, ...userWithoutPassword } = user
      return json(userWithoutPassword)
    } catch (error) {
      console.error('Get user error:', error)
      return json({ message: 'Interner Serverfehler' }, { status: 500 })
    }
  },
})
