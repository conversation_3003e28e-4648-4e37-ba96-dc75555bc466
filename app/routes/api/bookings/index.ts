import { createAPIFileRoute } from '@tanstack/react-start/api'
import { json } from '@tanstack/react-start'
import { storage } from '../../../utils/storage'
import { getSession } from '../../../utils/session'

export const Route = createAPIFileRoute('/api/bookings')({
  GET: async () => {
    try {
      const session = await getSession()
      
      if (!session.userId) {
        return json({ message: 'Nicht authentifiziert' }, { status: 401 })
      }

      const bookings = await storage.getBookingsByUser(session.userId)
      return json(bookings)
    } catch (error) {
      console.error('Get bookings error:', error)
      return json({ message: 'Interner Serverfehler' }, { status: 500 })
    }
  },

  POST: async ({ request }) => {
    try {
      const session = await getSession()
      
      if (!session.userId) {
        return json({ message: 'Nicht authentifiziert' }, { status: 401 })
      }

      const bookingData = await request.json()
      
      if (!bookingData.shipId || !bookingData.dockId || !bookingData.arrivalDate || !bookingData.departureDate) {
        return json({ message: 'Schiff, Anlegestelle, Ankunfts- und Abfahrtsdatum sind erforderlich' }, { status: 400 })
      }

      // Verify ship belongs to user
      const ship = await storage.getShip(bookingData.shipId)
      if (!ship || ship.userId !== session.userId) {
        return json({ message: 'Schiff nicht gefunden oder Zugriff verweigert' }, { status: 403 })
      }

      // Verify dock exists
      const dock = await storage.getDock(bookingData.dockId)
      if (!dock) {
        return json({ message: 'Anlegestelle nicht gefunden' }, { status: 404 })
      }

      const booking = await storage.createBooking({
        ...bookingData,
        userId: session.userId,
        status: 'pending', // Default status
      })

      return json(booking, { status: 201 })
    } catch (error) {
      console.error('Create booking error:', error)
      return json({ message: 'Interner Serverfehler' }, { status: 500 })
    }
  },
})
