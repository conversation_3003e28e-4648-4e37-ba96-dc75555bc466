import { createFileRoute, redirect } from '@tanstack/react-router'
import { createServerFn } from '@tanstack/react-start'
import { useState } from 'react'
import { Ship as ShipIcon, Anchor, BarChart, Calendar, Plus } from 'lucide-react'
import { format } from 'date-fns'
import { de } from 'date-fns/locale'

import { getSession } from '../utils/session'
import { storage } from '../utils/storage'
import type { BookingWithDetails, Ship as ShipType, User } from '../utils/storage'

// Server functions
const getUser = createServerFn({ method: 'GET' }).handler(async () => {
  const session = await getSession()
  if (!session.userId) {
    return null
  }
  
  const user = await storage.getUser(session.userId)
  if (!user) {
    return null
  }
  
  const { password: _, ...userWithoutPassword } = user
  return userWithoutPassword
})

const getDashboardData = createServerFn({ method: 'GET' }).handler(async () => {
  const session = await getSession()
  if (!session.userId) {
    throw new Error('Not authenticated')
  }

  const [bookings, ships, docks] = await Promise.all([
    storage.getBookingsByUser(session.userId),
    storage.getShipsByUser(session.userId),
    storage.getAllDocks(),
  ])

  return { bookings, ships, docks }
})

export const Route = createFileRoute('/')({
  beforeLoad: async () => {
    const user = await getUser()
    if (!user) {
      throw redirect({ to: '/login' })
    }
  },
  loader: async () => {
    const [user, dashboardData] = await Promise.all([
      getUser(),
      getDashboardData(),
    ])
    return { user, ...dashboardData }
  },
  component: DashboardPage,
})

function DashboardPage() {
  const { user, bookings, ships, docks } = Route.useLoaderData()
  const [isBookingFormOpen, setIsBookingFormOpen] = useState(false)

  // Calculate stats
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  
  const currentTime = new Date().getTime()

  const activeBookings = bookings.filter((b: BookingWithDetails) => {
    const departureTime = new Date(b.departureDate).getTime()
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
    const sevenDaysAgoTime = sevenDaysAgo.getTime()
    
    return b.status !== "cancelled" && (departureTime >= currentTime || departureTime >= sevenDaysAgoTime)
  })
  
  const arrivalsToday = bookings.filter((b: BookingWithDetails) => {
    const arrivalDate = new Date(b.arrivalDate)
    arrivalDate.setHours(0, 0, 0, 0)
    return arrivalDate.getTime() === today.getTime() && b.status !== "cancelled"
  })

  const docksUsed = Array.from(new Set(activeBookings.map((b: BookingWithDetails) => b.dockId))).length

  // Format current date
  const currentDate = format(new Date(), "EEEE, dd. MMMM yyyy", { locale: de })

  // Get upcoming bookings (limited to 3)
  const upcomingBookings = [...activeBookings]
    .sort((a, b) => new Date(a.arrivalDate).getTime() - new Date(b.arrivalDate).getTime())
    .slice(0, 3)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation would go here - simplified for now */}
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="space-y-6">
            {/* Welcome Message */}
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
              <div>
                <h1 className="text-2xl font-bold">Willkommen zurück, {user?.companyName}</h1>
                <p className="text-gray-500">{currentDate}</p>
              </div>
              <button
                onClick={() => setIsBookingFormOpen(true)}
                className="mt-4 md:mt-0 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
              >
                <Plus className="mr-2 h-4 w-4" /> Neue Buchung
              </button>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <ShipIcon className="h-6 w-6 text-gray-400" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Aktive Buchungen
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {activeBookings.length}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Calendar className="h-6 w-6 text-gray-400" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Heutige Ankünfte
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {arrivalsToday.length}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Anchor className="h-6 w-6 text-gray-400" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Verfügbare Schiffe
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {ships.length}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <BarChart className="h-6 w-6 text-gray-400" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Anlegestellen genutzt
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {docksUsed}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Upcoming Bookings */}
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <div className="px-4 py-5 sm:px-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Kommende Buchungen
                </h3>
              </div>
              <ul className="divide-y divide-gray-200">
                {upcomingBookings.length > 0 ? (
                  upcomingBookings.map((booking: BookingWithDetails) => (
                    <li key={booking.id} className="px-4 py-4 sm:px-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="flex-shrink-0">
                            <ShipIcon className="h-6 w-6 text-gray-400" />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {booking.ship.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {booking.dock.name}
                            </div>
                          </div>
                        </div>
                        <div className="text-sm text-gray-500">
                          {format(new Date(booking.arrivalDate), 'dd.MM.yyyy HH:mm')}
                        </div>
                      </div>
                    </li>
                  ))
                ) : (
                  <li className="px-4 py-4 sm:px-6 text-center text-gray-500">
                    Keine kommenden Buchungen vorhanden
                  </li>
                )}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
