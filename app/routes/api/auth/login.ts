import { createAPIFileRoute } from '@tanstack/react-start/api'
import { json } from '@tanstack/react-start'
import { storage } from '../../../utils/storage'
import { comparePasswords, setSession } from '../../../utils/session'

export const Route = createAPIFileRoute('/api/auth/login')({
  POST: async ({ request }) => {
    try {
      const { email, password } = await request.json()

      if (!email || !password) {
        return json({ message: 'E-Mail und Passwort sind erforderlich' }, { status: 400 })
      }

      // First try to find user by email, then by username
      let user = await storage.getUserByEmail(email)
      if (!user) {
        user = await storage.getUserByUsername(email)
      }

      if (!user || !(await comparePasswords(password, user.password))) {
        return json({ message: 'Ungültige Anmeldedaten' }, { status: 401 })
      }

      // Set session
      await setSession({
        userId: user.id,
        userEmail: user.email,
      })

      // Return user without password
      const { password: _, ...userWithoutPassword } = user
      return json(userWithoutPassword)
    } catch (error) {
      console.error('Login error:', error)
      return json({ message: 'Interner Serverfehler' }, { status: 500 })
    }
  },
})
